<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Water Waves</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB 0%, #4682B4 100%);
            position: relative;
        }

        .wave-container {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 200%;
            height: 200px;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23ffffff'/%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23ffffff'/%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23ffffff'/%3E%3C/svg%3E") repeat-x;
            animation: wave-animation 10s linear infinite;
        }

        .wave:nth-child(1) {
            bottom: 0;
            opacity: 0.7;
            animation-duration: 8s;
            height: 120px;
        }

        .wave:nth-child(2) {
            bottom: 10px;
            opacity: 0.5;
            animation-duration: 12s;
            animation-direction: reverse;
            height: 140px;
        }

        .wave:nth-child(3) {
            bottom: 20px;
            opacity: 0.3;
            animation-duration: 15s;
            height: 160px;
        }

        .wave:nth-child(4) {
            bottom: 30px;
            opacity: 0.2;
            animation-duration: 18s;
            animation-direction: reverse;
            height: 180px;
        }

        @keyframes wave-animation {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        /* Alternative simple wave using CSS shapes */
        .simple-wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: #4682B4;
            overflow: hidden;
        }

        .simple-wave::before {
            content: '';
            position: absolute;
            top: -50px;
            left: -50%;
            width: 200%;
            height: 200px;
            background: radial-gradient(ellipse at center, transparent 40%, #4682B4 40%);
            animation: simple-wave-animation 6s ease-in-out infinite;
        }

        .simple-wave::after {
            content: '';
            position: absolute;
            top: -30px;
            left: -50%;
            width: 200%;
            height: 150px;
            background: radial-gradient(ellipse at center, transparent 35%, rgba(70, 130, 180, 0.8) 35%);
            animation: simple-wave-animation 8s ease-in-out infinite reverse;
        }

        @keyframes simple-wave-animation {
            0%, 100% {
                transform: translateX(0) rotate(0deg);
            }
            50% {
                transform: translateX(-25%) rotate(180deg);
            }
        }

        /* Content overlay */
        .content {
            position: relative;
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: white;
            text-align: center;
            font-family: Arial, sans-serif;
        }

        .content h1 {
            font-size: 3rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 1rem;
        }

        .content p {
            font-size: 1.2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        /* Toggle button to switch between wave styles */
        .toggle-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-family: Arial, sans-serif;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            z-index: 100;
        }

        .toggle-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="content">
        <div>
            <h1>CSS Water Waves</h1>
            <p>Pure CSS animated water waves background</p>
        </div>
    </div>

    <button class="toggle-btn" onclick="toggleWaves()">Switch Wave Style</button>

    <!-- SVG-based waves (default) -->
    <div class="wave-container" id="svg-waves">
        <div class="wave"></div>
        <div class="wave"></div>
        <div class="wave"></div>
        <div class="wave"></div>
    </div>

    <!-- Simple CSS waves (alternative) -->
    <div class="simple-wave hidden" id="simple-waves"></div>

    <script>
        function toggleWaves() {
            const svgWaves = document.getElementById('svg-waves');
            const simpleWaves = document.getElementById('simple-waves');
            
            svgWaves.classList.toggle('hidden');
            simpleWaves.classList.toggle('hidden');
        }
    </script>
</body>
</html>
