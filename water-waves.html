<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Water Waves</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB 0%, #4682B4 50%, #2E86AB 100%);
            position: relative;
        }

        .ocean {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 60%;
            background: linear-gradient(to bottom, rgba(46, 134, 171, 0.8) 0%, #1e5f74 100%);
            overflow: hidden;
        }

        .wave {
            position: absolute;
            width: 200%;
            height: 100px;
            background: transparent;
            border-radius: 50%;
        }

        .wave::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(0deg, rgba(255,255,255,0.4) 0%, transparent 50%);
            border-radius: 50%;
            animation: wave-move 8s ease-in-out infinite;
        }

        .wave::after {
            content: '';
            position: absolute;
            top: 10px;
            left: 0;
            width: 100%;
            height: 80%;
            background: linear-gradient(0deg, rgba(255,255,255,0.2) 0%, transparent 50%);
            border-radius: 50%;
            animation: wave-move 8s ease-in-out infinite;
            animation-delay: -2s;
        }

        .wave1 {
            bottom: 0;
            animation: wave1 12s linear infinite;
            opacity: 0.8;
        }

        .wave2 {
            bottom: 20px;
            animation: wave2 15s linear infinite reverse;
            opacity: 0.6;
        }

        .wave3 {
            bottom: 40px;
            animation: wave3 18s linear infinite;
            opacity: 0.4;
        }

        .wave4 {
            bottom: 60px;
            animation: wave4 20s linear infinite reverse;
            opacity: 0.3;
        }

        @keyframes wave1 {
            0% { transform: translateX(-100%) rotateZ(0deg); }
            100% { transform: translateX(0%) rotateZ(360deg); }
        }

        @keyframes wave2 {
            0% { transform: translateX(-100%) rotateZ(0deg); }
            100% { transform: translateX(0%) rotateZ(-360deg); }
        }

        @keyframes wave3 {
            0% { transform: translateX(-100%) rotateZ(0deg); }
            100% { transform: translateX(0%) rotateZ(360deg); }
        }

        @keyframes wave4 {
            0% { transform: translateX(-100%) rotateZ(0deg); }
            100% { transform: translateX(0%) rotateZ(-360deg); }
        }

        @keyframes wave-move {
            0%, 100% { transform: translateY(0px) scaleY(1); }
            50% { transform: translateY(-20px) scaleY(1.1); }
        }

        /* Foam and bubbles effect */
        .foam {
            position: absolute;
            top: -20px;
            left: 0;
            width: 100%;
            height: 40px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255,255,255,0.6) 25%,
                rgba(255,255,255,0.8) 50%,
                rgba(255,255,255,0.6) 75%,
                transparent 100%);
            border-radius: 50%;
            animation: foam-move 6s ease-in-out infinite;
        }

        @keyframes foam-move {
            0%, 100% {
                transform: translateX(-10px) scaleX(1);
                opacity: 0.8;
            }
            50% {
                transform: translateX(10px) scaleX(1.1);
                opacity: 1;
            }
        }

        /* Water surface reflection */
        .water-surface {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg,
                transparent 0%,
                rgba(255,255,255,0.1) 30%,
                transparent 60%,
                rgba(255,255,255,0.05) 100%);
            animation: surface-shimmer 4s ease-in-out infinite;
        }

        @keyframes surface-shimmer {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        /* Content overlay */
        .content {
            position: relative;
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: white;
            text-align: center;
            font-family: Arial, sans-serif;
        }

        .content h1 {
            font-size: 3rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 1rem;
        }

        .content p {
            font-size: 1.2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        /* Additional wave effects */
        .wave-crest {
            position: absolute;
            top: -5px;
            left: 0;
            width: 100%;
            height: 10px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            animation: crest-move 3s ease-in-out infinite;
        }

        @keyframes crest-move {
            0%, 100% {
                transform: scaleX(0.8) translateY(0px);
                opacity: 0.6;
            }
            50% {
                transform: scaleX(1.2) translateY(-3px);
                opacity: 1;
            }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="content">
        <div>
            <h1>CSS Water Waves</h1>
            <p>Pure CSS animated water waves background</p>
        </div>
    </div>

    <button class="toggle-btn" onclick="toggleWaves()">Switch Wave Style</button>

    <!-- Ocean with realistic waves -->
    <div class="ocean" id="ocean-waves">
        <div class="water-surface"></div>
        <div class="wave wave1">
            <div class="foam"></div>
        </div>
        <div class="wave wave2">
            <div class="foam"></div>
        </div>
        <div class="wave wave3">
            <div class="foam"></div>
        </div>
        <div class="wave wave4">
            <div class="foam"></div>
        </div>
    </div>

    <script>
        function toggleWaves() {
            const svgWaves = document.getElementById('svg-waves');
            const simpleWaves = document.getElementById('simple-waves');
            
            svgWaves.classList.toggle('hidden');
            simpleWaves.classList.toggle('hidden');
        }
    </script>
</body>
</html>
